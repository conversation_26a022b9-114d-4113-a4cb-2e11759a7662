package tanks.generator;

import tanks.Game;
import tanks.gui.screen.ScreenPartyHost;

import java.util.ArrayList;
import java.util.Random;

public class LevelGeneratorRandom extends LevelGenerator
{
	public static String generateLevelString()
	{
		return generateLevelString(-1);
	}

	public static String generateLevelString(int seed)
	{
		Random random = initializeRandom(seed);
		double size = calculateLevelSize(random);
		int[] dimensions = calculateDimensions(size);
		int width = dimensions[0];
		int height = dimensions[1];

		double randomNum = random.nextDouble();
		double amountWalls = 12 * size * size;
		double amountTanks = 8 * size * size;
		int walls = (int) (randomNum * amountWalls + 4);

		int[] colors = calculateColors(random);
		int r = colors[0];
		int g = colors[1];
		int b = colors[2];

		double[] lightingConfig = calculateLighting(random);
		double light = lightingConfig[0];
		double shadeFactor = lightingConfig[1];
		boolean dark = light < 50;
		int numLights = (int) (walls / 5 + random.nextDouble() * 6 + 1);

		int time = calculateTime(random, randomNum, amountTanks, size);

		TerrainFeatures terrainFeatures = configureTerrainFeatures(random);
		BeatBlockConfig beatBlockConfig = configureBeatBlocks(random);
		EnvironmentalFeatures envFeatures = configureEnvironmentalFeatures(random, walls);
		TeleporterConfig teleporterConfig = configureTeleporters(random, walls);
		ExplosiveConfig explosiveConfig = configureExplosives(random, walls);

		StringBuilder s = new StringBuilder("{" + width + "," + height + "," + r + "," + g + "," + b + ",20,20,20," + time + "," + (int) light + "," + (int) (light * shadeFactor) + "|");

		GenerationArrays arrays = initializeArrays(width, height);

		generateWalls(random, walls, width, height, s, arrays, terrainFeatures, beatBlockConfig);
		generateTerrain(random, envFeatures, width, height, s, arrays, terrainFeatures.heavyTerrain);
		generateBoostPanels(random, envFeatures, width, height, s, arrays);
		generateExplosives(random, explosiveConfig, width, height, s, arrays);
		generateLights(random, dark, numLights, width, height, s, arrays);
		generateTeleporters(random, teleporterConfig, width, height, s, arrays);

		s.append("|");

		generateTanks(random, randomNum, amountTanks, width, height, s, arrays);

		if (!validateConnectivity(arrays, width, height))
		{
			return LevelGeneratorRandom.generateLevelString(seed);
		}

		return s.toString();
	}

	protected static Random initializeRandom(int seed)
	{
		if (seed != -1)
			return new Random(seed);
		else
			return new Random();
	}

	protected static double calculateLevelSize(Random random)
	{
		double size = Game.levelSize;

		if (random.nextDouble() < 0.3)
			size *= 2;

		if (Game.players.size() > 10)
			size *= 2;

		if (Game.players.size() > 40)
			size *= 2;

		if (random.nextDouble() < 0.3)
			size *= 2;

		return size;
	}

	protected static int[] calculateDimensions(double size)
	{
		int height = (int)(18 * size);
		int width = (int)(28 * size);
		return new int[]{width, height};
	}

	protected static int[] calculateColors(Random random)
	{
		int shade = 185;

		if (random.nextDouble() < 0.2)
			shade = (int) (random.nextDouble() * 60);

		int r = (int)(random.nextDouble() * 50) + shade;
		int g = (int)(random.nextDouble() * 50) + shade;
		int b = (int)(random.nextDouble() * 50) + shade;

		return new int[]{r, g, b};
	}

	protected static double[] calculateLighting(Random random)
	{
		double light = 100;
		double shadeFactor = 0.5;

		if (random.nextDouble() < 0.2)
		{
			light *= random.nextDouble() * 1.25;
		}

		if (random.nextDouble() < 0.2)
			shadeFactor = random.nextDouble() * 0.6 + 0.2;

		return new double[]{light, shadeFactor};
	}

	protected static int calculateTime(Random random, double randomNum, double amountTanks, double size)
	{
		int time = (int) (randomNum * amountTanks + 4) * 5;

		if (random.nextDouble() > 0.2)
			time = 0;
		else
			time += (int) (45 * (size / Game.levelSize - 1));

		return time;
	}

	protected static class TerrainFeatures
	{
		public double heavyTerrain;
		public boolean bouncy;
		public double bouncyWeight;
		public boolean nobounce;
		public double noBounceWeight;

		public TerrainFeatures(double heavyTerrain, boolean bouncy, double bouncyWeight, boolean nobounce, double noBounceWeight)
		{
			this.heavyTerrain = heavyTerrain;
			this.bouncy = bouncy;
			this.bouncyWeight = bouncyWeight;
			this.nobounce = nobounce;
			this.noBounceWeight = noBounceWeight;
		}
	}

	protected static class BeatBlockConfig
	{
		public boolean beatBlocks;
		public double beatBlocksWeight;
		public ArrayList<Integer> beatBlocksKinds;

		public BeatBlockConfig(boolean beatBlocks, double beatBlocksWeight, ArrayList<Integer> beatBlocksKinds)
		{
			this.beatBlocks = beatBlocks;
			this.beatBlocksWeight = beatBlocksWeight;
			this.beatBlocksKinds = beatBlocksKinds;
		}
	}

	protected static class EnvironmentalFeatures
	{
		public boolean shrubs;
		public int shrubCount;
		public boolean mud;
		public int mudCount;
		public boolean ice;
		public int iceCount;
		public boolean snow;
		public int snowCount;
		public boolean boostPanels;
		public int boostCount;

		public EnvironmentalFeatures(boolean shrubs, int shrubCount, boolean mud, int mudCount, boolean ice, int iceCount, boolean snow, int snowCount, boolean boostPanels, int boostCount)
		{
			this.shrubs = shrubs;
			this.shrubCount = shrubCount;
			this.mud = mud;
			this.mudCount = mudCount;
			this.ice = ice;
			this.iceCount = iceCount;
			this.snow = snow;
			this.snowCount = snowCount;
			this.boostPanels = boostPanels;
			this.boostCount = boostCount;
		}
	}

	protected static class TeleporterConfig
	{
		public boolean teleporters;
		public int numTeleporters;
		public int teleporterGroups;

		public TeleporterConfig(boolean teleporters, int numTeleporters, int teleporterGroups)
		{
			this.teleporters = teleporters;
			this.numTeleporters = numTeleporters;
			this.teleporterGroups = teleporterGroups;
		}
	}

	protected static class ExplosiveConfig
	{
		public boolean explosives;
		public int numExplosives;

		public ExplosiveConfig(boolean explosives, int numExplosives)
		{
			this.explosives = explosives;
			this.numExplosives = numExplosives;
		}
	}

	protected static class GenerationArrays
	{
		public int[][] teleporterArray;
		public boolean[][] solid;
		public boolean[][] cells;
		public double[][] cellWeights;
		public ArrayList<Integer[]> startPointsH;
		public ArrayList<Integer[]> startPointsV;
		public int[] tankX;
		public int[] tankY;
		public int[] playerTankX;
		public int[] playerTankY;

		public GenerationArrays(int width, int height)
		{
			teleporterArray = new int[width][height];
			solid = new boolean[width][height];
			cells = new boolean[width][height];
			cellWeights = new double[width][height];
			startPointsH = new ArrayList<>();
			startPointsV = new ArrayList<>();
		}
	}

	protected static TerrainFeatures configureTerrainFeatures(Random random)
	{
		double heavyTerrain = 1;

		if (random.nextDouble() < 0.2)
			heavyTerrain *= 2;

		if (random.nextDouble() < 0.2)
			heavyTerrain *= 2;

		if (random.nextDouble() < 0.2)
			heavyTerrain *= 4;

		boolean bouncy = random.nextDouble() < 0.2;
		double bouncyWeight = random.nextDouble() * 0.5 + 0.2;

		boolean nobounce = random.nextDouble() < 0.2;
		double noBounceWeight = random.nextDouble() * 0.5 + 0.2;

		return new TerrainFeatures(heavyTerrain, bouncy, bouncyWeight, nobounce, noBounceWeight);
	}

	protected static BeatBlockConfig configureBeatBlocks(Random random)
	{
		boolean beatBlocks = random.nextDouble() < 0.2;
		double beatBlocksWeight = random.nextDouble() * 0.5 + 0.2;
		ArrayList<Integer> beatBlocksKinds = new ArrayList<>();
		double br = random.nextDouble();
		if (br < 0.5)
			beatBlocksKinds.add(0);
		else if (br < 0.7)
			beatBlocksKinds.add(1);
		else if (br < 0.8)
			beatBlocksKinds.add(2);
		else if (br < 0.85)
		{
			beatBlocksKinds.add(0);
			beatBlocksKinds.add(1);
		}
		else if (br < 0.9)
		{
			beatBlocksKinds.add(0);
			beatBlocksKinds.add(2);
		}
		else if (br < 0.95)
		{
			beatBlocksKinds.add(1);
			beatBlocksKinds.add(2);
		}
		else
		{
			beatBlocksKinds.add(0);
			beatBlocksKinds.add(1);
			beatBlocksKinds.add(2);
		}

		if (random.nextDouble() < 0.05)
			beatBlocksKinds.add(3);

		return new BeatBlockConfig(beatBlocks, beatBlocksWeight, beatBlocksKinds);
	}

	protected static EnvironmentalFeatures configureEnvironmentalFeatures(Random random, int walls)
	{
		boolean shrubs = random.nextDouble() < 0.2;
		int shrubCount = (int) (walls + random.nextDouble() * 4 - 2);

		boolean mud = random.nextDouble() < 0.2;
		int mudCount = (int) (walls + random.nextDouble() * 4 - 2);

		boolean ice = random.nextDouble() < 0.2;
		int iceCount = (int) (walls + random.nextDouble() * 4 - 2);

		boolean snow = random.nextDouble() < 0.2;
		int snowCount = (int) (walls + random.nextDouble() * 4 - 2);

		boolean boostPanels = random.nextDouble() < 0.2;
		int boostCount = (int) (walls + random.nextDouble() * 4 - 2);

		return new EnvironmentalFeatures(shrubs, shrubCount, mud, mudCount, ice, iceCount, snow, snowCount, boostPanels, boostCount);
	}

	protected static TeleporterConfig configureTeleporters(Random random, int walls)
	{
		boolean teleporters = random.nextDouble() < 0.1;
		int numTeleporters = walls / 5 + 2;
		int teleporterGroups = (int) ((numTeleporters - 1) * 0.5 * random.nextDouble()) + 1;

		return new TeleporterConfig(teleporters, numTeleporters, teleporterGroups);
	}

	protected static ExplosiveConfig configureExplosives(Random random, int walls)
	{
		boolean explosives = random.nextDouble() < 0.2;
		int numExplosives = (int) (walls / 5 + random.nextDouble() * 4 + 1);

		return new ExplosiveConfig(explosives, numExplosives);
	}

	protected static GenerationArrays initializeArrays(int width, int height)
	{
		GenerationArrays arrays = new GenerationArrays(width, height);

		for (int i = 0; i < arrays.teleporterArray.length; i++)
		{
			for (int j = 0; j < arrays.teleporterArray[0].length; j++)
			{
				arrays.teleporterArray[i][j] = -1;
			}
		}

		for (int i = 0; i < width; i++)
		{
			for (int j = 0; j < height; j++)
			{
				arrays.cellWeights[i][j] = 1;
			}
		}

		return arrays;
	}

	protected static void generateWalls(Random random, int walls, int width, int height, StringBuilder s, GenerationArrays arrays, TerrainFeatures terrainFeatures, BeatBlockConfig beatBlockConfig)
	{
		int vertical = 2;
		int horizontal = 2;

		for (int i = 0; i < walls; i++)
		{
			int l = 1 + (int) Math.max(1, (random.nextDouble() * (Math.min(height, width) - 3)));

			WallTypeResult wallType = determineWallType(random, terrainFeatures, beatBlockConfig);
			String type = wallType.type;
			boolean passable = wallType.passable;

			if (random.nextDouble() * (vertical + horizontal) < horizontal)
			{
				generateHorizontalWall(random, width, height, l, type, passable, s, arrays, vertical++);
			}
			else
			{
				generateVerticalWall(random, width, height, l, type, passable, s, arrays, horizontal++);
			}

			if (i < walls - 1)
			{
				if (!s.toString().endsWith(","))
					s.append(",");
			}
		}
	}

	protected static class WallTypeResult
	{
		public String type;
		public boolean passable;

		public WallTypeResult(String type, boolean passable)
		{
			this.type = type;
			this.passable = passable;
		}
	}

	protected static WallTypeResult determineWallType(Random random, TerrainFeatures terrainFeatures, BeatBlockConfig beatBlockConfig)
	{
		String type = "";
		boolean passable = true;

		if (terrainFeatures.bouncy && random.nextDouble() < terrainFeatures.bouncyWeight)
			type = "-bouncy";
		else if (beatBlockConfig.beatBlocks && random.nextDouble() < beatBlockConfig.beatBlocksWeight)
		{
			type = "-beat-" + (int) ((beatBlockConfig.beatBlocksKinds.get((int) (random.nextDouble() * beatBlockConfig.beatBlocksKinds.size())) + random.nextDouble()) * 2);
			passable = true;
		}
		else if (terrainFeatures.nobounce && random.nextDouble() < terrainFeatures.noBounceWeight)
		{
			type = "-nobounce";
			passable = false;
		}
		else if (random.nextDouble() < 0.5)
		{
			type = "-hard";
			passable = false;
		}
		else if (random.nextDouble() < 0.25)
		{
			type = "-hole";
			passable = false;
		}
		else if (random.nextDouble() < 0.25)
		{
			type = "-breakable";
			passable = true;
		}

		return new WallTypeResult(type, passable);
	}

	protected static void generateHorizontalWall(Random random, int width, int height, int l, String type, boolean passable, StringBuilder s, GenerationArrays arrays, int vertical)
	{
		int x = 0;
		int y = 0;
		int xEnd = 0;
		int yEnd = 0;
		int rand;
		Integer[] sp = null;

		if (random.nextDouble() < 0.25 || arrays.startPointsH.isEmpty())
		{
			for (int in = 0; in < 50; in++)
			{
				boolean chosen = false;

				int attempts = 0;
				while (!chosen && attempts < 100)
				{
					attempts++;

					x = (int) (random.nextDouble() * (width - l));
					y = (int) (random.nextDouble() * (height));
					xEnd = x + l;
					yEnd = y;

					double weight = 0;
					for (int x1 = x; x1 <= xEnd; x1++)
					{
						weight += arrays.cellWeights[x1][y];
					}
					weight /= (xEnd - x + 1);

					if (random.nextDouble() < weight)
						chosen = true;
				}

				boolean stop = false;

				for (int x1 = x - 2; x1 <= xEnd + 2; x1++)
				{
					for (int y1 = y - 2; y1 <= yEnd + 2; y1++)
					{
						if (arrays.cells[Math.max(0, Math.min(width-1, x1))][Math.max(0, Math.min(height-1, y1))])
						{
							stop = true;
							break;
						}
					}

					if (stop)
						break;
				}

				if (!stop)
					break;
			}
		}
		else
		{
			rand = (int) (random.nextDouble() * arrays.startPointsH.size());
			x = arrays.startPointsH.get(rand)[0] + 1;
			y = arrays.startPointsH.get(rand)[1];
			xEnd = x + l + 1;
			yEnd = y;
			sp = arrays.startPointsH.remove(rand);

			if ((random.nextDouble() < 0.5 && x > 1) || x >= width)
			{
				xEnd -= l + 2;
				x -= l + 2;
			}
		}

		x = Math.max(x, 0);
		xEnd = Math.min(xEnd, width - 1);

		if (sp == null || sp[0] != x || sp[1] != y)
			arrays.startPointsV.add(new Integer[]{x, y});

		if (sp == null || sp[0] != xEnd || sp[1] != yEnd)
			arrays.startPointsV.add(new Integer[]{xEnd, yEnd});

		boolean started = false;
		boolean stopped = false;

		for (int z = x; z <= xEnd; z++)
		{
			if (!arrays.cells[z][y])
			{
				if (!started)
				{
					if (stopped)
					{
						s.append("-").append(y);
						s.append(type);
						s.append(",");
						stopped = false;
					}

					s.append(z).append("...");
					started = true;
				}

				arrays.cells[z][y] = true;
				arrays.solid[z][y] = arrays.solid[z][y] || !passable;
			}
			else
			{
				if (started)
				{
					started = false;
					stopped = true;
					s.append(z - 1);
				}
			}
		}

		if (started)
		{
			s.append(xEnd);
		}

		if (started || stopped)
		{
			s.append("-").append(y);
			s.append(type);
		}

		for (int j = Math.max(0, x - 5); j <= Math.min(xEnd + 5, width - 1); j++)
		{
			for (int k = Math.max(0, y - 5); k <= Math.min(yEnd + 5, height - 1); k++)
			{
				arrays.cellWeights[j][k] /= 2;
			}
		}
	}

	protected static void generateVerticalWall(Random random, int width, int height, int l, String type, boolean passable, StringBuilder s, GenerationArrays arrays, int horizontal)
	{
		int x = 0;
		int y = 0;
		int xEnd = 0;
		int yEnd = 0;
		int rand;
		Integer[] sp = null;

		if (random.nextDouble() < 0.25 || arrays.startPointsV.isEmpty())
		{
			for (int in = 0; in < 50; in++)
			{
				boolean chosen = false;

				int attempts = 0;
				while (!chosen && attempts < 100)
				{
					attempts++;

					x = (int) (random.nextDouble() * (width));
					y = (int) (random.nextDouble() * (height - l));
					xEnd = x;
					yEnd = y + l;

					double weight = 0;
					for (int y1 = y; y1 <= yEnd; y1++)
					{
						weight += arrays.cellWeights[x][y1];
					}
					weight /= (yEnd - y + 1);

					if (random.nextDouble() < weight)
						chosen = true;
				}

				boolean stop = false;

				for (int x1 = x - 2; x1 <= xEnd + 2; x1++)
				{
					for (int y1 = y - 2; y1 <= yEnd + 2; y1++)
					{
						if (arrays.cells[Math.max(0, Math.min(width - 1, x1))][Math.max(0, Math.min(height - 1, y1))])
						{
							stop = true;
							break;
						}
					}

					if (stop)
						break;
				}

				if (!stop)
					break;
			}
		}
		else
		{
			rand = (int) (random.nextDouble() * arrays.startPointsV.size());
			x = arrays.startPointsV.get(rand)[0];
			y = arrays.startPointsV.get(rand)[1] + 1;
			xEnd = x;
			yEnd = y + l + 1;
			sp = arrays.startPointsV.remove(rand);

			if ((random.nextDouble() < 0.5 && y > 1) || y >= height)
			{
				yEnd -= l + 2;
				y -= l + 2;
			}
		}

		y = Math.max(y, 0);
		yEnd = Math.min(yEnd, height - 1);

		if (sp == null || sp[0] != x || sp[1] != y)
			arrays.startPointsH.add(new Integer[]{x, y});

		if (sp == null || sp[0] != xEnd || sp[1] != yEnd)
			arrays.startPointsH.add(new Integer[]{xEnd, yEnd});

		boolean started = false;
		boolean stopped = false;

		for (int z = y; z <= yEnd; z++)
		{
			if (!arrays.cells[x][z])
			{
				if (!started)
				{
					if (stopped)
					{
						s.append(type);
						s.append(",");
						stopped = false;
					}

					s.append(x).append("-").append(z).append("...");
					started = true;
				}

				arrays.cells[x][z] = true;
				arrays.solid[x][z] = arrays.solid[x][z] || !passable;
			}
			else
			{
				if (started)
				{
					s.append(z - 1);
					started = false;
					stopped = true;
				}
			}
		}

		if (started)
		{
			s.append(yEnd);
		}

		if (started || stopped)
		{
			s.append(type);
		}

		for (int j = Math.max(0, x - 5); j <= Math.min(xEnd + 5, width - 1); j++)
		{
			for (int k = Math.max(0, y - 5); k <= Math.min(yEnd + 5, height - 1); k++)
			{
				arrays.cellWeights[j][k] /= 2;
			}
		}
	}

	protected static void generateTerrain(Random random, EnvironmentalFeatures envFeatures, int width, int height, StringBuilder s, GenerationArrays arrays, double heavyTerrain)
	{
		if (envFeatures.shrubs)
		{
			generateShrubs(random, envFeatures.shrubCount, width, height, s, arrays, heavyTerrain);
		}

		if (envFeatures.mud)
		{
			generateMud(random, envFeatures.mudCount, width, height, s, arrays, heavyTerrain);
		}

		if (envFeatures.ice)
		{
			generateIce(random, envFeatures.iceCount, width, height, s, arrays, heavyTerrain);
		}

		if (envFeatures.snow)
		{
			generateSnow(random, envFeatures.snowCount, width, height, s, arrays, heavyTerrain);
		}
	}

	protected static void generateShrubs(Random random, int shrubCount, int width, int height, StringBuilder s, GenerationArrays arrays, double heavyTerrain)
	{
		for (int j = 0; j < shrubCount; j++)
		{
			int x = (int) (random.nextDouble() * width);
			int y = (int) (random.nextDouble() * height);

			for (int i = 0; i < (random.nextDouble() * 20 + 4) * heavyTerrain; i++)
			{
				if (x < width && y < height && x >= 0 && y >= 0 && !arrays.cells[x][y])
				{
					arrays.cells[x][y] = true;

					if (!s.toString().endsWith(","))
						s.append(",");

					s.append(x).append("-").append(y).append("-shrub");
				}

				double rand = random.nextDouble();

				if (rand < 0.25)
					x++;
				else if (rand < 0.5)
					x--;
				else if (rand < 0.75)
					y++;
				else
					y--;
			}
		}
	}

	protected static void generateMud(Random random, int mudCount, int width, int height, StringBuilder s, GenerationArrays arrays, double heavyTerrain)
	{
		for (int j = 0; j < mudCount; j++)
		{
			int x = (int) (random.nextDouble() * width);
			int y = (int) (random.nextDouble() * height);

			for (int i = 0; i < (random.nextDouble() * 20 + 4) * heavyTerrain; i++)
			{
				if (x < width && y < height && x >= 0 && y >= 0 && !arrays.cells[x][y])
				{
					arrays.cells[x][y] = true;

					if (!s.toString().endsWith(","))
						s.append(",");

					s.append(x).append("-").append(y).append("-mud");
				}

				double rand = random.nextDouble();

				if (rand < 0.25)
					x++;
				else if (rand < 0.5)
					x--;
				else if (rand < 0.75)
					y++;
				else
					y--;
			}
		}
	}

	protected static void generateIce(Random random, int iceCount, int width, int height, StringBuilder s, GenerationArrays arrays, double heavyTerrain)
	{
		for (int j = 0; j < iceCount; j++)
		{
			int x = (int) (random.nextDouble() * width);
			int y = (int) (random.nextDouble() * height);

			for (int i = 0; i < (random.nextDouble() * 40 + 8) * heavyTerrain; i++)
			{
				if (x < width && y < height && x >= 0 && y >= 0 && !arrays.cells[x][y])
				{
					arrays.cells[x][y] = true;

					if (!s.toString().endsWith(","))
						s.append(",");

					s.append(x).append("-").append(y).append("-ice");
				}

				double rand = random.nextDouble();

				if (rand < 0.25)
					x++;
				else if (rand < 0.5)
					x--;
				else if (rand < 0.75)
					y++;
				else
					y--;
			}
		}
	}

	protected static void generateSnow(Random random, int snowCount, int width, int height, StringBuilder s, GenerationArrays arrays, double heavyTerrain)
	{
		for (int j = 0; j < snowCount; j++)
		{
			int x = (int) (random.nextDouble() * width);
			int y = (int) (random.nextDouble() * height);

			for (int i = 0; i < (random.nextDouble() * 40 + 8) * heavyTerrain; i++)
			{
				if (x < width && y < height && x >= 0 && y >= 0 && !arrays.cells[x][y])
				{
					arrays.cells[x][y] = true;

					if (!s.toString().endsWith(","))
						s.append(",");

					s.append(x).append("-").append(y).append("-snow");
				}

				double rand = random.nextDouble();

				if (rand < 0.25)
					x++;
				else if (rand < 0.5)
					x--;
				else if (rand < 0.75)
					y++;
				else
					y--;
			}
		}
	}

	protected static void generateBoostPanels(Random random, EnvironmentalFeatures envFeatures, int width, int height, StringBuilder s, GenerationArrays arrays)
	{
		if (envFeatures.boostPanels)
		{
			for (int j = 0; j < envFeatures.boostCount; j++)
			{
				int x1 = (int) (random.nextDouble() * width);
				int y1 = (int) (random.nextDouble() * height);

				int panelSize = (int)(random.nextDouble() * 3) + 1;

				for (int x = x1; x < x1 + panelSize; x++)
				{
					for (int y = y1; y < y1 + panelSize; y++)
					{
						if (x < width && y < height && x >= 0 && y >= 0 && !arrays.cells[x][y])
						{
							arrays.cells[x][y] = true;

							if (!s.toString().endsWith(","))
								s.append(",");

							s.append(x).append("-").append(y).append("-boostpanel");
						}
					}
				}
			}
		}
	}

	protected static void generateExplosives(Random random, ExplosiveConfig explosiveConfig, int width, int height, StringBuilder s, GenerationArrays arrays)
	{
		if (explosiveConfig.explosives)
		{
			for (int j = 0; j < explosiveConfig.numExplosives; j++)
			{
				int x = (int) (random.nextDouble() * width);
				int y = (int) (random.nextDouble() * height);

				if (x < width && y < height && x >= 0 && y >= 0 && !arrays.cells[x][y])
				{
					arrays.cells[x][y] = true;

					if (!s.toString().endsWith(","))
						s.append(",");

					s.append(x).append("-").append(y).append("-explosive");
				}
			}
		}
	}

	protected static void generateLights(Random random, boolean dark, int numLights, int width, int height, StringBuilder s, GenerationArrays arrays)
	{
		if (dark)
		{
			for (int j = 0; j < numLights; j++)
			{
				int x = (int) (random.nextDouble() * width);
				int y = (int) (random.nextDouble() * height);

				if (x < width && y < height && x >= 0 && y >= 0 && !arrays.cells[x][y])
				{
					arrays.cells[x][y] = true;

					if (!s.toString().endsWith(","))
						s.append(",");

					s.append(x).append("-").append(y).append("-light-").append((int)(random.nextDouble() * 5 + 1) / 2.0);
				}
			}
		}
	}

	protected static void generateTeleporters(Random random, TeleporterConfig teleporterConfig, int width, int height, StringBuilder s, GenerationArrays arrays)
	{
		if (teleporterConfig.teleporters)
		{
			int n = teleporterConfig.numTeleporters;
			int groupProgress = 0;

			while (n > 0)
			{
				int x = (int) (random.nextDouble() * width);
				int y = (int) (random.nextDouble() * height);

				if (!arrays.cells[x][y])
				{
					for (int i = Math.max(x - 2, 0); i <= Math.min(x + 2, width - 1); i++)
						for (int j = Math.max(y - 2, 0); j <= Math.min(y + 2, height - 1); j++)
							arrays.cells[i][j] = true;

					if (!s.toString().endsWith(","))
						s.append(",");

					int id = groupProgress / 2;

					if (n == 1)
						id = (groupProgress - 1) / 2;

					groupProgress++;

					if (id >= teleporterConfig.teleporterGroups)
						id = (int) (random.nextDouble() * teleporterConfig.teleporterGroups);

					s.append(x).append("-").append(y).append("-teleporter");
					arrays.teleporterArray[x][y] = id;

					if (id != 0)
						s.append("-").append(id);

					n--;
				}
			}
		}
	}

	protected static void generateTanks(Random random, double randomNum, double amountTanks, int width, int height, StringBuilder s, GenerationArrays arrays)
	{
		s.append("|");

		int numTanks = (int) (randomNum * amountTanks + 1);
		int[] tankX = new int[numTanks];
		int[] tankY = new int[numTanks];

		int numPlayers = 1;

		if (ScreenPartyHost.isServer)
			numPlayers = Game.players.size();

		int[] playerTankX = new int[numPlayers];
		int[] playerTankY = new int[numPlayers];

		generatePlayerTanks(random, numPlayers, width, height, s, arrays, playerTankX, playerTankY);
		generateEnemyTanks(random, numTanks, width, height, s, arrays, tankX, tankY);

		// Store tank positions for validation
		arrays.tankX = tankX;
		arrays.tankY = tankY;
		arrays.playerTankX = playerTankX;
		arrays.playerTankY = playerTankY;
	}

	protected static void generatePlayerTanks(Random random, int numPlayers, int width, int height, StringBuilder s, GenerationArrays arrays, int[] playerTankX, int[] playerTankY)
	{
		for (int i = 0; i < numPlayers; i++)
		{
			int angle = (int) (random.nextDouble() * 4);
			int x = (int) (random.nextDouble() * (width));
			int y = (int) (random.nextDouble() * (height));

			int attempts2 = 0;
			while (arrays.cells[x][y] && attempts2 < 100)
			{
				attempts2++;
				x = (int) (random.nextDouble() * (width));
				y = (int) (random.nextDouble() * (height));
			}

			int bound = calculatePlayerBound(numPlayers);

			for (int a = -bound; a <= bound; a++)
				for (int j = -bound; j <= bound; j++)
					arrays.cells[Math.max(0, Math.min(width - 1, x+a))][Math.max(0, Math.min(height - 1, y+j))] = true;

			s.append(x).append("-").append(y).append("-");
			s.append("player");
			s.append("-").append(angle);

			playerTankX[i] = x;
			playerTankY[i] = y;

			s.append(",");
		}
	}

	protected static int calculatePlayerBound(int numPlayers)
	{
		if (numPlayers < 20)
			return 2;
		else if (numPlayers < 56)
			return 1;
		else
			return 0;
	}

	protected static void generateEnemyTanks(Random random, int numTanks, int width, int height, StringBuilder s, GenerationArrays arrays, int[] tankX, int[] tankY)
	{
		for (int i = 0; i < numTanks; i++)
		{
			int angle = (int) (random.nextDouble() * 4);
			int x = (int) (random.nextDouble() * (width));
			int y = (int) (random.nextDouble() * (height));

			int attempts2 = 0;
			while (arrays.cells[x][y] && attempts2 < 100)
			{
				attempts2++;
				x = (int) (random.nextDouble() * (width));
				y = (int) (random.nextDouble() * (height));
			}

			for (int a = -1; a <= 1; a++)
				for (int j = -1; j <= 1; j++)
					arrays.cells[Math.max(0, Math.min(width - 1, x+a))][Math.max(0, Math.min(height - 1, y+j))] = true;

			s.append(x).append("-").append(y).append("-");
			s.append(Game.registryTank.getRandomTank(random).name);
			s.append("-").append(angle);

			tankX[i] = x;
			tankY[i] = y;

			if (i == numTanks - 1)
			{
				s.append("}");
			}
			else
			{
				s.append(",");
			}
		}
	}

	protected static boolean validateConnectivity(GenerationArrays arrays, int width, int height)
	{
		ArrayList<Integer> currentX = new ArrayList<>();
		ArrayList<Integer> currentY = new ArrayList<>();

		currentX.add(arrays.playerTankX[0]);
		currentY.add(arrays.playerTankY[0]);

		while (!currentX.isEmpty())
		{
			int posX = currentX.remove(0);
			int posY = currentY.remove(0);

			arrays.solid[posX][posY] = true;

			if (arrays.teleporterArray[posX][posY] >= 0)
			{
				int id = arrays.teleporterArray[posX][posY];

				for (int i = 0; i < width; i++)
				{
					for (int j = 0; j < height; j++)
					{
						if (arrays.teleporterArray[i][j] == id && !(posX == i && posY == j) && !arrays.solid[i][j])
						{
							currentX.add(i);
							currentY.add(j);
						}
					}
				}
			}

			if (posX > 0 && !arrays.solid[posX - 1][posY])
			{
				currentX.add(posX - 1);
				currentY.add(posY);
				arrays.solid[posX - 1][posY] = true;
			}

			if (posX < width - 1 && !arrays.solid[posX + 1][posY])
			{
				currentX.add(posX + 1);
				currentY.add(posY);
				arrays.solid[posX + 1][posY] = true;
			}

			if (posY > 0 && !arrays.solid[posX][posY - 1])
			{
				currentX.add(posX);
				currentY.add(posY - 1);
				arrays.solid[posX][posY - 1] = true;
			}

			if (posY < height - 1 && !arrays.solid[posX][posY + 1])
			{
				currentX.add(posX);
				currentY.add(posY + 1);
				arrays.solid[posX][posY + 1] = true;
			}
		}

		for (int i = 0; i < arrays.tankX.length; i++)
		{
			if (!arrays.solid[arrays.tankX[i]][arrays.tankY[i]])
			{
				return false;
			}
		}

		for (int i = 1; i < arrays.playerTankX.length; i++)
		{
			if (!arrays.solid[arrays.playerTankX[i]][arrays.playerTankY[i]])
			{
				return false;
			}
		}

		return true;
	}
}