package tanks.generator;

import tanks.Game;
import tanks.item.Item;
import tanks.translation.Translation;

import java.util.ArrayList;
import java.util.Random;

public class LevelGeneratorVersus extends LevelGeneratorRandom
{
	public static String generateLevelString() {
		return generateLevelString(-1);
	}

	public static String generateLevelString(int seed)
	{
		Random random = initializeRandom(seed);
		double size = calculateLevelSize(random);
		int[] dimensions = calculateDimensions(size);
		int width = dimensions[0];
		int height = dimensions[1];
		
		double randomNum = random.nextDouble();
		double amountWalls = 12 * size * size;
		int walls = (int) (randomNum * amountWalls + 4);

		int[] colors = calculateVersusColors(random);
		int r = colors[0];
		int g = colors[1];
		int b = colors[2];
		
		double[] lightingConfig = calculateLighting(random);
		double light = lightingConfig[0];
		double shadeFactor = lightingConfig[1];
		boolean dark = light < 50;
		int numLights = (int) (walls / 5 + random.nextDouble() * 6 + 1);
		
		int time = calculateVersusTime(random, size);
		
		TerrainFeatures terrainFeatures = configureTerrainFeatures(random);
		BeatBlockConfig beatBlockConfig = configureBeatBlocks(random);
		EnvironmentalFeatures envFeatures = configureEnvironmentalFeatures(random, walls);
		TeleporterConfig teleporterConfig = configureTeleporters(random, walls);
		ExplosiveConfig explosiveConfig = configureExplosives(random, walls);

		StringBuilder itemsString = generateShopItems();
		StringBuilder s = new StringBuilder(itemsString.toString() + "level\n{" + width + "," + height + "," + r + "," + g + "," + b + ",20,20,20," + time + "," + (int) light + "," + (int) (light * shadeFactor) + "|");

		GenerationArrays arrays = initializeArrays(width, height);
		
		generateWalls(random, walls, width, height, s, arrays, terrainFeatures, beatBlockConfig);
		generateTerrain(random, envFeatures, width, height, s, arrays, terrainFeatures.heavyTerrain);
		generateBoostPanels(random, envFeatures, width, height, s, arrays);
		generateExplosives(random, explosiveConfig, width, height, s, arrays);
		generateLights(random, dark, numLights, width, height, s, arrays);
		generateTeleporters(random, teleporterConfig, width, height, s, arrays);

		s.append("|");

		generateVersusPlayerTanks(random, width, height, s, arrays);
		
		if (!validateVersusConnectivity(arrays, width, height))
		{
			return LevelGeneratorVersus.generateLevelString(seed);
		}

		return s.toString();
	}

	protected static int[] calculateVersusColors(Random random)
	{
		int shade = 185;

		if (random.nextDouble() < 0.2)
			shade = 30;

		int r = (int)(random.nextDouble() * 50) + shade;
		int g = (int)(random.nextDouble() * 50) + shade;
		int b = (int)(random.nextDouble() * 50) + shade;
		
		return new int[]{r, g, b};
	}

	protected static int calculateVersusTime(Random random, double size)
	{
		int time = (int) (random.nextDouble() * 24 + 12 * size) * 5;

		if (random.nextDouble() > 0.2)
			time = 0;

		return time;
	}

	protected static StringBuilder generateShopItems()
	{
		StringBuilder itemsString = new StringBuilder("coins\n50\nshop\n");
		ArrayList<String> items = Game.game.fileManager.getInternalFileContents("/items/items.tanks");

		for (String si: items)
		{
			Item.ItemStack<?> i = Item.ItemStack.fromString(null, si);
			int price;

			switch (i.item.name)
			{
				case "Fire bullet":
                case "Block":
                case "Mini bullet":
                case "Artillery shell":
                    price = 5;
					break;
				case "Bouncy fire bullet":
                case "Dark fire bullet":
                case "Booster":
                case "Explosive bullet":
                case "Freezing bullet":
                    price = 10;
					break;
				case "Mega mine":
                case "Homing bullet":
                case "Healing ray":
                    price = 25;
					break;
				case "Zap":
                case "Mega bullet":
                    price = 15;
					break;
				case "Shield":
					price = 50;
					break;
                case "Flamethrower":
					price = 4;
					break;
                case "Air":
					price = 8;
					break;
                default:
					continue;
			}

			i.item.name = Translation.translate(i.item.name);
			Item.ShopItem s = new Item.ShopItem(i);
			s.price = price;
			itemsString.append(s.toString()).append("\n");
		}

		return itemsString;
	}

	protected static void generateVersusPlayerTanks(Random random, int width, int height, StringBuilder s, GenerationArrays arrays)
	{
		int numTanks = Game.players.size();
		int[] playerX = new int[numTanks - 1];
		int[] playerY = new int[numTanks - 1];
		int firstPlayerX = 0;
		int firstPlayerY = 0;

		for (int i = 0; i < numTanks; i++)
		{
			int angle = (int) (random.nextDouble() * 4);
			int x = (int) (random.nextDouble() * (width));
			int y = (int) (random.nextDouble() * (height));

			int attempts1 = 0;
			while (arrays.cells[x][y] && attempts1 < 100)
			{
				attempts1++;
				x = (int) (random.nextDouble() * (width));
				y = (int) (random.nextDouble() * (height));
			}

			int bound = calculateVersusPlayerBound(numTanks);

			for (int a = -bound; a <= bound; a++)
				for (int j = -bound; j <= bound; j++)
					arrays.cells[Math.max(0, Math.min(width - 1, x+a))][Math.max(0, Math.min(height - 1, y+j))] = true;

			s.append(x).append("-").append(y).append("-");
			s.append("player");
			s.append("-").append(angle);

			if (i == 0)
			{
				firstPlayerX = x;
				firstPlayerY = y;
			}
			else
			{
				playerX[i - 1] = x;
				playerY[i - 1] = y;
			}

			if (i == numTanks - 1)
			{
				s.append("|ally-true}");
			}
			else
			{
				s.append(",");
			}
		}

		// Store tank positions for validation
		arrays.playerTankX = new int[]{firstPlayerX};
		arrays.playerTankY = new int[]{firstPlayerY};
		arrays.tankX = playerX;
		arrays.tankY = playerY;
	}

	protected static int calculateVersusPlayerBound(int numTanks)
	{
		if (numTanks < 4)
			return 8;
		else if (numTanks < 6)
			return 4;
		else if (numTanks < 10)
			return 3;
		else if (numTanks < 20)
			return 2;
		else if (numTanks < 56)
			return 1;
		else
			return 0;
	}

	protected static boolean validateVersusConnectivity(GenerationArrays arrays, int width, int height)
	{
		ArrayList<Integer> currentX = new ArrayList<>();
		ArrayList<Integer> currentY = new ArrayList<>();

		currentX.add(arrays.playerTankX[0]);
		currentY.add(arrays.playerTankY[0]);

		while (!currentX.isEmpty())
		{
			int posX = currentX.remove(0);
			int posY = currentY.remove(0);

			arrays.solid[posX][posY] = true;

			if (arrays.teleporterArray[posX][posY] >= 0)
			{
				int id = arrays.teleporterArray[posX][posY];

				for (int i = 0; i < width; i++)
				{
					for (int j = 0; j < height; j++)
					{
						if (arrays.teleporterArray[i][j] == id && !(posX == i && posY == j) && !arrays.solid[i][j])
						{
							currentX.add(i);
							currentY.add(j);
						}
					}
				}
			}

			if (posX > 0 && !arrays.solid[posX - 1][posY])
			{
				currentX.add(posX - 1);
				currentY.add(posY);
				arrays.solid[posX - 1][posY] = true;
			}

			if (posX < width - 1 && !arrays.solid[posX + 1][posY])
			{
				currentX.add(posX + 1);
				currentY.add(posY);
				arrays.solid[posX + 1][posY] = true;
			}

			if (posY > 0 && !arrays.solid[posX][posY - 1])
			{
				currentX.add(posX);
				currentY.add(posY - 1);
				arrays.solid[posX][posY - 1] = true;
			}

			if (posY < height - 1 && !arrays.solid[posX][posY + 1])
			{
				currentX.add(posX);
				currentY.add(posY + 1);
				arrays.solid[posX][posY + 1] = true;
			}
		}

		for (int i = 0; i < arrays.tankX.length; i++)
		{
			if (!arrays.solid[arrays.tankX[i]][arrays.tankY[i]])
			{
				return false;
			}
		}

		return true;
	}
}
